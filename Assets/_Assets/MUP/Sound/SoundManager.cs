using System.Collections.Generic;
using MUP.Core;
using UnityEngine;

namespace MUP.Sound
{
    public class SoundManager : MUPSingleton<SoundManager>
    {
        private bool isMutedSfx;
        private bool isMutedBgm;
        private Dictionary<string, AudioClip> sfxDictionary;
        private Dictionary<string, AudioClip> bgmDictionary;
        private GameObject prefabSfxSource;
        private AudioSource bgmSource;
        private List<AudioSource> sfxSources;

        public void Init()
        {
            isMutedSfx = PlayerPrefs.GetInt("isMutedSfx", 0) == 1;
            isMutedBgm = PlayerPrefs.GetInt("isMutedBgm", 0) == 1;

            sfxDictionary = new Dictionary<string, AudioClip>();
            bgmDictionary = new Dictionary<string, AudioClip>();

            bgmSource = gameObject.AddComponent(typeof(AudioSource)) as AudioSource;
            sfxSources = new List<AudioSource>();

            PoolManager.Instance.InitObjectPool(prefabSfxSource, 10, 20);
        }

        public void MuteSfx(bool mute)
        {
            isMutedSfx = mute;
            PlayerPrefs.SetInt("isMutedSfx", mute ? 1 : 0);
        }

        public void MuteBgm(bool mute)
        {
            isMutedBgm = mute;
            PlayerPrefs.SetInt("isMutedBgm", mute ? 1 : 0);
        }

        public void PlaySfx(string sfxName)
        {
            if (isMutedSfx) return;
            GameObject go = PoolManager.Instance.Spawn(prefabSfxSource);
            AudioSource sfxSource = go.GetComponent<AudioSource>();
            sfxSources.Add(sfxSource);
            sfxSource.clip = sfxDictionary[sfxName];
            sfxSource.Play();
        }

        public void StopSfx()
        {
            if (isMutedSfx) return;
            foreach (AudioSource sfxSource in sfxSources)
            {
                sfxSource.Stop();
            }
        }

        public void PauseSfx()
        {
            if (isMutedSfx) return;
            foreach (AudioSource sfxSource in sfxSources)
            {
                sfxSource.Pause();
            }
        }

        public void UnPauseSfx()
        {
            if (isMutedSfx) return;
            foreach (AudioSource sfxSource in sfxSources)
            {
                sfxSource.UnPause();
            }
        }

        public void PlayBgm(string bgmName, bool isLoop = true)
        {
            if (isMutedBgm) return;
            bgmSource.clip = bgmDictionary[bgmName];
            bgmSource.loop = isLoop;
            bgmSource.Play();
        }

        public void StopBgm()
        {
            if (isMutedBgm) return;
            bgmSource.Stop();
        }

        public void PauseBgm()
        {
            if (isMutedBgm) return;
            bgmSource.Pause();
        }

        public void UnPauseBgm()
        {
            if (isMutedBgm) return;
            bgmSource.UnPause();
        }
    }
}