using MUP.Core;
using UnityEngine;
using System.Collections.Generic;

namespace MUP.UI
{
    public class UIManager : MUP<PERSON>ingleton<UIManager>
    {
        [SerializeField] private Canvas sceneCanvas;
        [SerializeField] private Canvas popupCanvas;

        private Stack<MUPView> sceneStack = new Stack<MUPView>();
        private Stack<MUPView> popupStack = new Stack<MUPView>();

        public void Init()
        {

        }

        public void ShowScene(string viewId)
        {

        }

        public void HideScene(string viewId)
        {

        }

        public void ShowPopup(string viewId)
        {

        }


        public void HidePopup(string viewId)
        {

        }

        public MUPView GetCurrentScene()
        {
            return sceneStack.Count > 0 ? sceneStack.Peek() : null;
        }

        public MUPView GetCurrentPopup()
        {
            return popupStack.Count > 0 ? popupStack.Peek() : null;
        }
    }
}
