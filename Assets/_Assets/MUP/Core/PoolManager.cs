using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Pool;

namespace MUP.Core
{
    public interface IPoolableObject
    {
        void OnObjectSpawn();
        void OnObjectRelease();
    }

    public class PoolManager : MUPSingleton<PoolManager>
    {
        private Dictionary<GameObject, IObjectPool<GameObject>> pools = new Dictionary<GameObject, IObjectPool<GameObject>>();

        public void Init()
        {

        }

        public void InitObjectPool(GameObject prefab, int defaultCapacity, int maxSize)
        {
            IObjectPool<GameObject> pool = new ObjectPool<GameObject>(
                createFunc: () => CreatePooledItem(prefab),
                actionOnGet: OnGetFromPool,
                actionOnRelease: OnReleaseToPool,
                actionOnDestroy: OnDestroyPoolObject,
                collectionCheck: false,
                defaultCapacity: defaultCapacity,
                maxSize: maxSize
            );

            pools.Add(prefab, pool);
        }

        public GameObject Spawn(GameObject prefab)
        {
            IObjectPool<GameObject> pool = GetPool(prefab);
            GameObject obj = pool.Get();
            return obj;
        }

        public void Release(GameObject obj, GameObject prefab)
        {
            if (pools.TryGetValue(prefab, out IObjectPool<GameObject> pool))
            {
                pool.Release(obj);
            }
            else
            {
                Destroy(obj);
            }
        }

        private IObjectPool<GameObject> GetPool(GameObject prefab)
        {
            if (pools.TryGetValue(prefab, out IObjectPool<GameObject> pool))
            {
                return pool;
            }

            Debug.LogError($"[PoolManager] Pool not found for prefab: : {prefab.name}");
            return null;
        }

        private GameObject CreatePooledItem(GameObject prefab)
        {
            GameObject obj = Instantiate(prefab);
            obj.transform.SetParent(transform);
            return obj;
        }

        private void OnGetFromPool(GameObject obj)
        {
            if (obj.TryGetComponent<IPoolableObject>(out var poolable))
            {
                poolable.OnObjectSpawn();
            }
        }

        private void OnReleaseToPool(GameObject obj)
        {
            if (obj.TryGetComponent<IPoolableObject>(out var poolable))
            {
                poolable.OnObjectRelease();
            }
        }

        private void OnDestroyPoolObject(GameObject obj)
        {
            Destroy(obj);
        }
    }
}