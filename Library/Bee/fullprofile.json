{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 75155, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 75155, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 75155, "tid": 7394, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 75155, "tid": 7394, "ts": 1751883865775705, "dur": 414, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 75155, "tid": 7394, "ts": 1751883865778819, "dur": 588, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 75155, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 75155, "tid": 1, "ts": 1751883865332332, "dur": 12130, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 75155, "tid": 1, "ts": 1751883865344466, "dur": 64976, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 75155, "tid": 1, "ts": 1751883865409450, "dur": 30358, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 75155, "tid": 7394, "ts": 1751883865779411, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 75155, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865329895, "dur": 1256, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865331153, "dur": 438475, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865331960, "dur": 4789, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865336795, "dur": 1515, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865338316, "dur": 4780, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865343102, "dur": 422, "ph": "X", "name": "ProcessMessages 8178", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865343526, "dur": 31, "ph": "X", "name": "ReadAsync 8178", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865343559, "dur": 3, "ph": "X", "name": "ProcessMessages 8168", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865343563, "dur": 29, "ph": "X", "name": "ReadAsync 8168", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865343651, "dur": 42, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865343695, "dur": 2, "ph": "X", "name": "ProcessMessages 2376", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865343698, "dur": 509, "ph": "X", "name": "ReadAsync 2376", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865344293, "dur": 4, "ph": "X", "name": "ProcessMessages 8141", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865344297, "dur": 37, "ph": "X", "name": "ReadAsync 8141", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865344336, "dur": 1, "ph": "X", "name": "ProcessMessages 2378", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865344337, "dur": 47, "ph": "X", "name": "ReadAsync 2378", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865344387, "dur": 54, "ph": "X", "name": "ReadAsync 1519", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865344444, "dur": 25, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865344472, "dur": 228, "ph": "X", "name": "ReadAsync 1506", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865344701, "dur": 1, "ph": "X", "name": "ProcessMessages 2628", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865344704, "dur": 725, "ph": "X", "name": "ReadAsync 2628", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865345430, "dur": 2, "ph": "X", "name": "ProcessMessages 3470", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865345433, "dur": 392, "ph": "X", "name": "ReadAsync 3470", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865345827, "dur": 1, "ph": "X", "name": "ProcessMessages 1758", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865345829, "dur": 97, "ph": "X", "name": "ReadAsync 1758", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865345933, "dur": 9, "ph": "X", "name": "ProcessMessages 4026", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865345944, "dur": 90, "ph": "X", "name": "ReadAsync 4026", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865346037, "dur": 2, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865346061, "dur": 74, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865346137, "dur": 4, "ph": "X", "name": "ProcessMessages 1910", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865346143, "dur": 76, "ph": "X", "name": "ReadAsync 1910", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865346238, "dur": 389, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865346631, "dur": 5, "ph": "X", "name": "ProcessMessages 1900", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865346638, "dur": 159, "ph": "X", "name": "ReadAsync 1900", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865346799, "dur": 2, "ph": "X", "name": "ProcessMessages 2922", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865346801, "dur": 220, "ph": "X", "name": "ReadAsync 2922", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347024, "dur": 1, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347025, "dur": 48, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347074, "dur": 1, "ph": "X", "name": "ProcessMessages 1912", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347202, "dur": 169, "ph": "X", "name": "ReadAsync 1912", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347374, "dur": 30, "ph": "X", "name": "ProcessMessages 3623", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347433, "dur": 92, "ph": "X", "name": "ReadAsync 3623", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347526, "dur": 1, "ph": "X", "name": "ProcessMessages 2040", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347528, "dur": 300, "ph": "X", "name": "ReadAsync 2040", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347830, "dur": 3, "ph": "X", "name": "ProcessMessages 5920", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347835, "dur": 101, "ph": "X", "name": "ReadAsync 5920", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347938, "dur": 1, "ph": "X", "name": "ProcessMessages 2100", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865347946, "dur": 131, "ph": "X", "name": "ReadAsync 2100", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348079, "dur": 1, "ph": "X", "name": "ProcessMessages 2120", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348080, "dur": 24, "ph": "X", "name": "ReadAsync 2120", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348106, "dur": 43, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348150, "dur": 1, "ph": "X", "name": "ProcessMessages 1422", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348152, "dur": 41, "ph": "X", "name": "ReadAsync 1422", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348196, "dur": 27, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348225, "dur": 123, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348350, "dur": 2, "ph": "X", "name": "ProcessMessages 2584", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348352, "dur": 109, "ph": "X", "name": "ReadAsync 2584", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348465, "dur": 12, "ph": "X", "name": "ProcessMessages 2648", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348479, "dur": 41, "ph": "X", "name": "ReadAsync 2648", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348521, "dur": 1, "ph": "X", "name": "ProcessMessages 2012", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348522, "dur": 105, "ph": "X", "name": "ReadAsync 2012", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348628, "dur": 1, "ph": "X", "name": "ProcessMessages 2440", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348630, "dur": 165, "ph": "X", "name": "ReadAsync 2440", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348796, "dur": 1, "ph": "X", "name": "ProcessMessages 2764", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348797, "dur": 92, "ph": "X", "name": "ReadAsync 2764", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348890, "dur": 2, "ph": "X", "name": "ProcessMessages 3749", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348893, "dur": 72, "ph": "X", "name": "ReadAsync 3749", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348966, "dur": 1, "ph": "X", "name": "ProcessMessages 1938", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865348967, "dur": 45, "ph": "X", "name": "ReadAsync 1938", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865349015, "dur": 42, "ph": "X", "name": "ReadAsync 1279", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865349058, "dur": 28, "ph": "X", "name": "ProcessMessages 1311", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865349087, "dur": 23, "ph": "X", "name": "ReadAsync 1311", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865349112, "dur": 4029, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353145, "dur": 5, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353151, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353182, "dur": 75, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353258, "dur": 1, "ph": "X", "name": "ProcessMessages 1620", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353260, "dur": 28, "ph": "X", "name": "ReadAsync 1620", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353290, "dur": 112, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353403, "dur": 1, "ph": "X", "name": "ProcessMessages 1709", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353404, "dur": 49, "ph": "X", "name": "ReadAsync 1709", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353456, "dur": 148, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353606, "dur": 1, "ph": "X", "name": "ProcessMessages 2228", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353608, "dur": 21, "ph": "X", "name": "ReadAsync 2228", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353631, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353633, "dur": 195, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353829, "dur": 2, "ph": "X", "name": "ProcessMessages 4837", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353926, "dur": 22, "ph": "X", "name": "ReadAsync 4837", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353949, "dur": 1, "ph": "X", "name": "ProcessMessages 2783", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865353952, "dur": 296, "ph": "X", "name": "ReadAsync 2783", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865354248, "dur": 3, "ph": "X", "name": "ProcessMessages 6306", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865354252, "dur": 18, "ph": "X", "name": "ReadAsync 6306", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865354273, "dur": 91, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865354365, "dur": 1, "ph": "X", "name": "ProcessMessages 2147", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865354367, "dur": 15, "ph": "X", "name": "ReadAsync 2147", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865354384, "dur": 16, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865354403, "dur": 94, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865356850, "dur": 342, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357194, "dur": 4, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357199, "dur": 18, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357219, "dur": 62, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357283, "dur": 1, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357284, "dur": 18, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357361, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357377, "dur": 1, "ph": "X", "name": "ProcessMessages 1710", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357378, "dur": 83, "ph": "X", "name": "ReadAsync 1710", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357462, "dur": 1, "ph": "X", "name": "ProcessMessages 1847", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357464, "dur": 23, "ph": "X", "name": "ReadAsync 1847", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357489, "dur": 59, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357668, "dur": 86, "ph": "X", "name": "ReadAsync 1342", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357756, "dur": 2, "ph": "X", "name": "ProcessMessages 5259", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865357759, "dur": 135, "ph": "X", "name": "ReadAsync 5259", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865358260, "dur": 1, "ph": "X", "name": "ProcessMessages 1270", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865358262, "dur": 25, "ph": "X", "name": "ReadAsync 1270", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865358288, "dur": 4, "ph": "X", "name": "ProcessMessages 8174", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865358293, "dur": 132, "ph": "X", "name": "ReadAsync 8174", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865358427, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865358429, "dur": 421, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865358854, "dur": 123, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865358980, "dur": 398, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865359423, "dur": 17, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865359442, "dur": 4829, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865364275, "dur": 5, "ph": "X", "name": "ProcessMessages 8190", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865364281, "dur": 108, "ph": "X", "name": "ReadAsync 8190", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865364391, "dur": 326, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865364720, "dur": 260, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865365062, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865365064, "dur": 14469, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865379541, "dur": 18, "ph": "X", "name": "ProcessMessages 8179", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865379561, "dur": 468, "ph": "X", "name": "ReadAsync 8179", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380037, "dur": 3, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380042, "dur": 205, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380250, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380291, "dur": 56, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380350, "dur": 2, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380354, "dur": 109, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380464, "dur": 1, "ph": "X", "name": "ProcessMessages 1629", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380466, "dur": 44, "ph": "X", "name": "ReadAsync 1629", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380513, "dur": 426, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380940, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865380942, "dur": 663, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865381609, "dur": 4, "ph": "X", "name": "ProcessMessages 1930", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865381615, "dur": 55, "ph": "X", "name": "ReadAsync 1930", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865381683, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865381686, "dur": 360, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865382049, "dur": 2, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865382052, "dur": 180, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865382234, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865382236, "dur": 251, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865382619, "dur": 2, "ph": "X", "name": "ProcessMessages 2703", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865382624, "dur": 145, "ph": "X", "name": "ReadAsync 2703", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865382772, "dur": 339, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865383129, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865383134, "dur": 258, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865383415, "dur": 7, "ph": "X", "name": "ProcessMessages 1620", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865383424, "dur": 360, "ph": "X", "name": "ReadAsync 1620", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865383789, "dur": 5, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865383824, "dur": 71, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865383903, "dur": 167, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865384072, "dur": 5, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865384080, "dur": 301, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865384428, "dur": 320, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865384751, "dur": 189, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865384941, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865384942, "dur": 221, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865385183, "dur": 5, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865385191, "dur": 451, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865385704, "dur": 41, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865385753, "dur": 1, "ph": "X", "name": "ProcessMessages 1372", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865385754, "dur": 264, "ph": "X", "name": "ReadAsync 1372", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865386023, "dur": 1, "ph": "X", "name": "ProcessMessages 1250", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865386024, "dur": 284, "ph": "X", "name": "ReadAsync 1250", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865386312, "dur": 59, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865386372, "dur": 95, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865386470, "dur": 177, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865386655, "dur": 253, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865386912, "dur": 133, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865387053, "dur": 17, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865387077, "dur": 295, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865387375, "dur": 188, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865387566, "dur": 243, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865387810, "dur": 32, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865387882, "dur": 126, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865388014, "dur": 210, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865388227, "dur": 118, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865388347, "dur": 121, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865388484, "dur": 1, "ph": "X", "name": "ProcessMessages 1217", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865388511, "dur": 23, "ph": "X", "name": "ReadAsync 1217", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865388538, "dur": 1, "ph": "X", "name": "ProcessMessages 1192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865388540, "dur": 526, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865389191, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865389192, "dur": 29, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865389223, "dur": 3, "ph": "X", "name": "ProcessMessages 2654", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865389227, "dur": 370, "ph": "X", "name": "ReadAsync 2654", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865389609, "dur": 87, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865389697, "dur": 238, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865389986, "dur": 268, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865390256, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865390345, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865390347, "dur": 27, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865390376, "dur": 166, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865390579, "dur": 179, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865390760, "dur": 10, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865390783, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865390849, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865390854, "dur": 195, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391051, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391139, "dur": 139, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391281, "dur": 168, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391451, "dur": 56, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391509, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391595, "dur": 137, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391734, "dur": 66, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391803, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391908, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865391912, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392030, "dur": 93, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392134, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392175, "dur": 145, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392348, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392409, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392531, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392553, "dur": 65, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392627, "dur": 185, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392875, "dur": 105, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865392983, "dur": 101, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393210, "dur": 63, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393275, "dur": 162, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393441, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393446, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393514, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393539, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393541, "dur": 129, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393673, "dur": 62, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393737, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393811, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865393915, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394077, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394179, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394265, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394391, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394393, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394421, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394532, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394590, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394728, "dur": 10, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394740, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394778, "dur": 96, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865394878, "dur": 133, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395013, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395085, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395215, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395343, "dur": 33, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395470, "dur": 159, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395637, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395662, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395707, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395709, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395740, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395801, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865395835, "dur": 449, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865396285, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865396287, "dur": 50, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865396433, "dur": 119, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865396556, "dur": 9, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865396568, "dur": 125, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865396695, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865396697, "dur": 103, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865396801, "dur": 104, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865396907, "dur": 269, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397218, "dur": 17, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397240, "dur": 135, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397385, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397387, "dur": 67, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397457, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397603, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397645, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397647, "dur": 161, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397809, "dur": 45, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397870, "dur": 48, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397961, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397967, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865397997, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398018, "dur": 163, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398183, "dur": 160, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398367, "dur": 131, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398500, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398564, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398565, "dur": 75, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398642, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398673, "dur": 122, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398797, "dur": 69, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398868, "dur": 9, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865398878, "dur": 7655, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865406581, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865406583, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865406635, "dur": 2509, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865409147, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865409149, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865409220, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865409291, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865409293, "dur": 2775, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865412072, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865412074, "dur": 17573, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865429651, "dur": 20, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865429673, "dur": 582, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865430258, "dur": 274, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865430534, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865430536, "dur": 257, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865431071, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865431130, "dur": 229, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865431362, "dur": 218, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865431583, "dur": 504, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865432091, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865432093, "dur": 127, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865432222, "dur": 181, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865432437, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865432439, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865432520, "dur": 358, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865433014, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865433076, "dur": 370, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865433447, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865433454, "dur": 250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865433710, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865433876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865433878, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865433970, "dur": 216, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865434255, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865434291, "dur": 634, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865434928, "dur": 109, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435039, "dur": 213, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435254, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435447, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435449, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435594, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435596, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435683, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435772, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435774, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865435907, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865436115, "dur": 204, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865436321, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865436447, "dur": 264, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865436713, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865436756, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865436854, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865436979, "dur": 269, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865437250, "dur": 487, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865437748, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865437863, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865438050, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865438255, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865438404, "dur": 222, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865438637, "dur": 305, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865438944, "dur": 298, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865439245, "dur": 229, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865439480, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865439484, "dur": 287, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865439774, "dur": 250, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865440050, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865440054, "dur": 490, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865440547, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865440550, "dur": 279, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865440832, "dur": 171, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441020, "dur": 136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441158, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441353, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441355, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441494, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441496, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441629, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441631, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441740, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441795, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441798, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441977, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865441980, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865442174, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865442177, "dur": 282, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865442462, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865442589, "dur": 391, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865442985, "dur": 257, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865443244, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865443384, "dur": 290, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865443676, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865443793, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865443902, "dur": 148, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865444052, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865444182, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865444353, "dur": 320, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865444674, "dur": 379, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865445054, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865445088, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865445090, "dur": 217, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865445313, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865445317, "dur": 366, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865445685, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865445691, "dur": 159, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865445870, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865445874, "dur": 406, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865446283, "dur": 171, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865446457, "dur": 571, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865447031, "dur": 177, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865447223, "dur": 262, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865447514, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865447519, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865447575, "dur": 298, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865447894, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865447950, "dur": 446, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865448407, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865448532, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865448535, "dur": 250, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865448788, "dur": 388, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865449178, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865449180, "dur": 108856, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865558043, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865558046, "dur": 68, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865558118, "dur": 50, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865558172, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865558248, "dur": 46, "ph": "X", "name": "ReadAsync 7896", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865558295, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865558335, "dur": 91, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865558427, "dur": 1391, "ph": "X", "name": "ProcessMessages 2033", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865559821, "dur": 2740, "ph": "X", "name": "ReadAsync 2033", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865562563, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865562566, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865562722, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865562970, "dur": 412, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865563386, "dur": 491, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865563879, "dur": 374, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865564256, "dur": 1288, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865565547, "dur": 1506, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865567058, "dur": 580, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865567645, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865567650, "dur": 1215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865568868, "dur": 717, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865569590, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865569605, "dur": 684, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865570292, "dur": 2425, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865572725, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865572727, "dur": 1299, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865574030, "dur": 4229, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865578265, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865578268, "dur": 1033, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865579306, "dur": 278, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865579603, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865579605, "dur": 1761, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865581370, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865581372, "dur": 4587, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865585966, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865585969, "dur": 853, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865586826, "dur": 903, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865587733, "dur": 2129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865589867, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865589869, "dur": 581, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865590454, "dur": 1482, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865591939, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865591941, "dur": 1127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865593073, "dur": 734, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865593820, "dur": 753, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865594576, "dur": 283, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865594862, "dur": 1010, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865595875, "dur": 1440, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865597322, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865597326, "dur": 518, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865597847, "dur": 299, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865598149, "dur": 243, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865598395, "dur": 127, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865598524, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865598625, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865598653, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865598685, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865598790, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865598921, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865598993, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599067, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599155, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599193, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599326, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599519, "dur": 185, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599711, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599715, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599778, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599846, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599872, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599905, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599937, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865599971, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600023, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600081, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600100, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600174, "dur": 78, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600253, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600259, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600297, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600382, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600476, "dur": 171, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600652, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600655, "dur": 119, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600778, "dur": 128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865600908, "dur": 125, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601035, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601132, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601255, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601304, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601343, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601548, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601574, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601689, "dur": 135, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601826, "dur": 69, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601897, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865601984, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865602060, "dur": 311, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865602374, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865602406, "dur": 607, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865603016, "dur": 394, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865603412, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865603432, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865603453, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865603557, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865603601, "dur": 17608, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865621215, "dur": 14, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865621230, "dur": 2536, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865623768, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865623769, "dur": 139572, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865763347, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865763349, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865763388, "dur": 69, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865763458, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865763485, "dur": 37, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865763523, "dur": 19, "ph": "X", "name": "ProcessMessages 4164", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865763543, "dur": 2664, "ph": "X", "name": "ReadAsync 4164", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865766209, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865766211, "dur": 332, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865766544, "dur": 15, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865766561, "dur": 73, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865766637, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865766772, "dur": 997, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 75155, "tid": 12884901888, "ts": 1751883865767773, "dur": 1492, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 75155, "tid": 7394, "ts": 1751883865779424, "dur": 782, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 75155, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 75155, "tid": 8589934592, "ts": 1751883865323818, "dur": 116139, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 75155, "tid": 8589934592, "ts": 1751883865439960, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 75155, "tid": 8589934592, "ts": 1751883865439964, "dur": 2148, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 75155, "tid": 7394, "ts": 1751883865780208, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 75155, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 75155, "tid": 4294967296, "ts": 1751883865254283, "dur": 516179, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751883865270623, "dur": 45867, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751883865770572, "dur": 3670, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751883865772145, "dur": 1280, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 75155, "tid": 4294967296, "ts": 1751883865774289, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 75155, "tid": 7394, "ts": 1751883865780213, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751883865319806, "dur": 2434, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751883865322246, "dur": 16303, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751883865338659, "dur": 182, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751883865339274, "dur": 4068, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751883865343739, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_0DC0DA8529CF1082.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751883865344227, "dur": 253, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_4782BF78D66FB2E5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751883865347228, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751883865349750, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751883865349815, "dur": 3546, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751883865355010, "dur": 2436, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751883865358481, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751883865361993, "dur": 2532, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751883865370855, "dur": 9003, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751883865338846, "dur": 50913, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751883865389768, "dur": 377102, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751883865766986, "dur": 477, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751883865338727, "dur": 51049, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865389797, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751883865390115, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_9FEDC77DF18D321C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865390295, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_7D5EE36C09E1C813.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865390482, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865390642, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_5A158695308CA418.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865390925, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865391052, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865391235, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865391365, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C9234425C371F41A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865391521, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_B519677B2E750245.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865391631, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865391728, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_6C9DA28BBAE3FB82.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865391863, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865391932, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_0DC0DA8529CF1082.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865392039, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865392149, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AEA8660476D6039F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865392242, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865392317, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_B15AC01DD6F6A05B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865392463, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865392549, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C59F701EDC7B3906.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865392670, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865392760, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_BA115901BA18BB2D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865392865, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865392947, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_06C0A05E69B9BD23.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865393080, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865393138, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_EB44D0B764B7C8DC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865393288, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865393375, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751883865393692, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865393781, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865394678, "dur": 11846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865406525, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865406677, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865406846, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865407809, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865408859, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865409935, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865410760, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865411713, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865413266, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/Selection/ITransformSelection.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751883865412630, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865414386, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865415454, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865416350, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865417469, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865418638, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865419835, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865420835, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865421855, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865422935, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865423901, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865424867, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865425902, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865426850, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865428210, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865429539, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865429749, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865430397, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865431169, "dur": 1451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865432620, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865432766, "dur": 1656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865434422, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865434567, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865434707, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865435477, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865435544, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865435642, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865435985, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865436077, "dur": 3750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865439828, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865439959, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865440387, "dur": 1675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865442062, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865442222, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865442324, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865442382, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865442800, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865443873, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865444002, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865444229, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865445122, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865445231, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865446002, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865446153, "dur": 1221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865447375, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751883865447583, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865448180, "dur": 111584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865559770, "dur": 3161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865562972, "dur": 3515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865566533, "dur": 2505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865569039, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865569163, "dur": 2244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865571408, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865571600, "dur": 7035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865578636, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865578757, "dur": 14376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865593134, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865593306, "dur": 4299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865597605, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751883865597669, "dur": 6094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751883865603787, "dur": 163091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865338731, "dur": 51073, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865389810, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751883865390087, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_E6099293577F65EB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865390278, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_8B7F52F4EF803EEA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865390463, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865390605, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E4ECE7E8BB35882.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865390899, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865391058, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FAD28CEFAD4EF7CD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865391420, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_110B7A9752F5AD38.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865391558, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865391645, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_C268CA3985788B50.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865391780, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865391869, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_6DF747A371F03CBE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865391982, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865392073, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_34F8FBC3386DE99E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865392212, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865392285, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_462079EBF000A463.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865392409, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865392522, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_65DFED6065744ECD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865392667, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865392737, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45806E81BE62D86B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865392850, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865392927, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5A2D3B4655B3286F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865393040, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865393105, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_0897B8A47BC70881.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865393224, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865393279, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_3FB038AB94CBD0EF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865393371, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865393446, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_8C284CAFF6588524.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865393525, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865393608, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A9C2D5B3A007F854.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865393756, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865393818, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_665A96337EB7495A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865393884, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865394001, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865394103, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865394187, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751883865394399, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865394507, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751883865394861, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865395018, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865395175, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865395287, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865395401, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865395458, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751883865395699, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865395773, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865395919, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865396016, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751883865396185, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865396260, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865396339, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865396403, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865396479, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865396537, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751883865396802, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865396933, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865397086, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865397242, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865397336, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751883865397511, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865397604, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751883865397669, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865397810, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865397879, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865398016, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865398092, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865398175, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751883865398330, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865398403, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865398505, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865398584, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865398687, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865398770, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865398876, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865399014, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865400081, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865400964, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865401886, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865402916, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865403774, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865404586, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865405429, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865406225, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865407217, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865408187, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865409357, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865409425, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865410264, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865411261, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865412077, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865413267, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/ICharacterDataProvider.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751883865413065, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865414813, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865415778, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865416773, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865417963, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865419069, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865420273, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865421162, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865422272, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865423272, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865424317, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865425249, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865426282, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865427347, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865428128, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865429342, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865429509, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865429753, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865430391, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865430733, "dur": 1437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865432171, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865432376, "dur": 2798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865435174, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865435269, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D33AAA747FCEED0A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865435399, "dur": 1218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865436617, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865436772, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865437303, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865437361, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865437712, "dur": 1409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865439122, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865439261, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865439345, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865440547, "dur": 2134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865442682, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865442881, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865443013, "dur": 2016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865445038, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865445149, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865445376, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865445811, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865445941, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865446628, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751883865446855, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865447653, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865447719, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865447778, "dur": 111983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865559765, "dur": 4647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865564413, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865564573, "dur": 5084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865569683, "dur": 4342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865574025, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865574346, "dur": 7110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865581456, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865581592, "dur": 1631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865583224, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865583542, "dur": 4313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865587857, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865587974, "dur": 6747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865594721, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865594826, "dur": 7635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751883865602510, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865602641, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751883865603203, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865603258, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751883865603792, "dur": 163054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865338736, "dur": 51078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865389818, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_A93D6DF8E051997E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865390273, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_FDF1DF7383BE0CAD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865390456, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865390589, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_E5822FB88B5F8525.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865390900, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865390977, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3D9BC3FD62E6039E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865391213, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865391338, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8192A56F15586443.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865391508, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865391574, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E00800116238B81E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865391710, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865391787, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_2D77F886ADC51F6A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865391895, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865392010, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_070DA0E44072E4AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865392131, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865392201, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A07FB6BEB1E7333E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865392289, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865392390, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7436A235A0073526.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865392517, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865392611, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_EE513D35A608BF93.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865392705, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865392812, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4574CE91C336EF7D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865392908, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865393026, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8F6AF5EA65F36C03.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865393102, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865393168, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751883865393399, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865393455, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865394492, "dur": 14603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865409096, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865409301, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865409369, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865409444, "dur": 2761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865412248, "dur": 17425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865429737, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865429828, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865430316, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865430390, "dur": 3178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865433569, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865433739, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865433851, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865434048, "dur": 3014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865437063, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865437179, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865437523, "dur": 1285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865438808, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865438937, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865439051, "dur": 2118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865441170, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865441328, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865441421, "dur": 1018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865442488, "dur": 2520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865445074, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865445288, "dur": 2710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865448039, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865448170, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865448614, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865448678, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865448922, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751883865449023, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865450841, "dur": 170538, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865622036, "dur": 1200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865623781, "dur": 199, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865763462, "dur": 276, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751883865624325, "dur": 139421, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751883865766419, "dur": 371, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751883865338812, "dur": 51032, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865389854, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_5651B02FF685D981.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865390250, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_5BD5A8CCC51B35E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865390365, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865390500, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_F0C4CE25855A5940.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865390742, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865390922, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_479F21D64DDF5A5C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865391066, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865391227, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_39B25D32CFFE745B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865391427, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_4475AF2AFFABA926.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865391561, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865391656, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_58154A82C8AEEED2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865391774, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865391864, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2598F284265933B9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865392002, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865392113, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9179AE1968B95645.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865392267, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_36001A2C42EABFDD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865392430, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865392500, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_ED9F64302AB7CA36.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865392639, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865392701, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_C1F8E474D4FC94AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865392824, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865392897, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6799CBAE2582DA9C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865393022, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865393088, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_4782BF78D66FB2E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865393192, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865393420, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751883865393684, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865393763, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_B3E6C0B422B85093.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865393897, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751883865394088, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865394144, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751883865394262, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865394331, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751883865394394, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865394537, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751883865394765, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865394866, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751883865395017, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865395163, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865395254, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865395390, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865395533, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751883865395740, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865395819, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865395979, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865396090, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865396188, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865396264, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865396342, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865396407, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751883865396611, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865396697, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865396806, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865396952, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865397107, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865397276, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865397353, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751883865397561, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865397640, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865397758, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865397866, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865397953, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398073, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398161, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398230, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398291, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398375, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398478, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398569, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398677, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398737, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398836, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865398973, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865399075, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865400087, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865400986, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865401947, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865402938, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865403695, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865404522, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865405371, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865406150, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865407149, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865408107, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865409218, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865410163, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865411110, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865411949, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865413275, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/MathUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751883865412856, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865414703, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865415738, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865416725, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865417882, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865418978, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865420194, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865421101, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865422204, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865423217, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865424253, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865425185, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865426183, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865427212, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865428638, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865429304, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865429516, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865429755, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865430484, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865431024, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865431751, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865431910, "dur": 1162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865433073, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865433157, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865433275, "dur": 3071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865436346, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865436423, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865436632, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865437006, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865439325, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865439576, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865439666, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865440711, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865440863, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865443183, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865443313, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865444406, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865444479, "dur": 1365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865445846, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865446236, "dur": 1028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865447265, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865447390, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751883865447661, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865448064, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865448139, "dur": 111646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865559787, "dur": 3397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865563184, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865563338, "dur": 3896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865567234, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865567347, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865569794, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865569970, "dur": 6345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865576316, "dur": 1802, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865578148, "dur": 7996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865586145, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865586295, "dur": 3782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865590077, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865590179, "dur": 12421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751883865602655, "dur": 984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751883865603677, "dur": 163190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865338820, "dur": 51046, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865389875, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_C5C1DFA646678D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865390289, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_A8B77A263A174862.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865390454, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865390573, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_46E58BD6F1D896EF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865390916, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865391036, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_2A0E14B20F8987CC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865391209, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865391331, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_97266B4C96017FCE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865391495, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865391560, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FD99BC8C7952C75B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865391693, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865391777, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_AEBC23A8E4FA79A7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865391899, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865392017, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_12CDA413F762C33F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865392152, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865392227, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_DB4D8D23A5102F12.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865392324, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865392445, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_165FE63856186A10.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865392551, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865392653, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A7772F47C16CAC56.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865392758, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865392831, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_60DE2D62F663D764.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865392925, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865393009, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D0B6A15278A42C4B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865393092, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865393159, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865393234, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_82FEB68469624F24.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865393335, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865393438, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_BA6BF233C73D6014.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865393564, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_74A9378003EBEF66.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865393708, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865393800, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_02823EF6668D812C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865393869, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865393934, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865394015, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751883865394347, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865394429, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751883865394568, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865394635, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751883865394862, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865395006, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865395158, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865395245, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865395351, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865395414, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865395493, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751883865395659, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865395717, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865395786, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865395944, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865396039, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865396138, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865396228, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865396310, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865396376, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865396458, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865396515, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865396575, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751883865396711, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865396855, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865397001, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865397156, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865397299, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865397404, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865397619, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865397689, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865397817, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865397888, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865397976, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398107, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398183, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398248, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398310, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398393, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398495, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398585, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398691, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398752, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865398863, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865399009, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865399109, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865400103, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865401010, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865401958, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865402986, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865403827, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865404654, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865405482, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865406303, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865407302, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865408287, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865409419, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865410288, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865411271, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865412123, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865413263, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@a55da47cc43f/Runtime/2D/Renderer2D.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751883865413141, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865414912, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865415887, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865416908, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865418074, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865419209, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865420374, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865421234, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865422347, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865423319, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865424358, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865425324, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865426323, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865427361, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865428178, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865429370, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865429518, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865429751, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865430332, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865430462, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865430588, "dur": 5508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865436096, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865436245, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865436532, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865436586, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865436905, "dur": 1492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865438398, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865438695, "dur": 1257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865439953, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865440064, "dur": 1875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865441940, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865442209, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865442677, "dur": 370, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1751883865443047, "dur": 2149, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1751883865445196, "dur": 1109, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1751883865442357, "dur": 3952, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865446309, "dur": 2365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865448674, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751883865448741, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865449007, "dur": 110775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865559782, "dur": 2675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865562457, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865562538, "dur": 5241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865567780, "dur": 1090, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865568883, "dur": 2812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865571696, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865571868, "dur": 7858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865579783, "dur": 11446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865591230, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865591317, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865593971, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865594081, "dur": 3930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751883865598011, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865598291, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865598438, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865598561, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865598625, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865598720, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865599063, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865599304, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865599529, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865599600, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865599754, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865600703, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865600918, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865601012, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865601213, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865601348, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865601563, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865602302, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865602532, "dur": 1095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751883865603687, "dur": 163157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865338828, "dur": 51056, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865389888, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A137065F34CC9C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865390175, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865390261, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_C5F3E4FEF96D893F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865390446, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865390568, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_FBD6848B40F9D84E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865390903, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865391016, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_DB3537FAE77D4E90.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865391219, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865391345, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_355EDFCE7F3816F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865391514, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865391579, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_89BDB88F64678315.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865391723, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_1D0F8F342DF5CF0F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865391882, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865391948, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D7B1BF987A36C198.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865392071, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865392162, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0462768E64EABD0A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865392258, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865392337, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_FF3C62C209E1F2AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865392480, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865392568, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A6AABF0E6ADA0B25.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865392685, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865392787, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8D21704F39F1057D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865392901, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865392972, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A8529918C73873A7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865393093, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865393202, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751883865393595, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865393696, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_03EB462F708254BF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865393855, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_8895869DB6F5BBF5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865393944, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865394044, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751883865394417, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751883865394575, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751883865394800, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865394905, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865395048, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751883865395215, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865395310, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865395416, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865395519, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751883865395694, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865395755, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865395847, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865395998, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396105, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396199, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396282, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396353, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396438, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396497, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396563, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396670, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396777, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865396923, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865397080, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865397201, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865397332, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751883865397506, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865397626, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865397695, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865397824, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865397910, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398028, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398142, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398215, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398275, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398363, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398467, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398557, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398656, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398728, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398831, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865398969, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865399072, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865400093, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865400993, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865401965, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865403001, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865403843, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865404666, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865405494, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865406298, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865407290, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865408265, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865409391, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865410283, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865411285, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865412128, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865413293, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@a55da47cc43f/Runtime/2D/Shadows/ShadowProvider/Providers/ShadowShape2DProvider_SpriteRenderer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751883865413109, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865414869, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865415853, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865416874, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865418058, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865419183, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865420361, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865421231, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865422361, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865423324, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865424367, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865425336, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865426336, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865427382, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865427930, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865429125, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865429520, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865429749, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865430394, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865430789, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865432414, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865432598, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865433292, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865433347, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865433939, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865434156, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865434839, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865435684, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865435932, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865436236, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865436294, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865436946, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865438127, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865439405, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865439565, "dur": 1364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865440930, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865441135, "dur": 2309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865443444, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865443589, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865443678, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751883865443779, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865444597, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865444693, "dur": 1221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865445915, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865446312, "dur": 113430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865559743, "dur": 4319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865564064, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865564182, "dur": 3538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865567722, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865567863, "dur": 6110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865573974, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865574162, "dur": 7055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865581217, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865581279, "dur": 9300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865590581, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865590668, "dur": 5284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865595953, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751883865596127, "dur": 7690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751883865603834, "dur": 163024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865338835, "dur": 51058, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865389897, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_661E9919313836F1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865390243, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_D7331C31AE624D95.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865390451, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865390699, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_9358C75F1190FC64.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865390956, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865391079, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_CA2F35FCE4A30BF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865391333, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_79266F7FBDBF49B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865391508, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865391568, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0D7C2BFD403ED278.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865391710, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865391808, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_5A04CF1DC9DD1390.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865391892, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865391969, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_CA0E1C7BC0C607E9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865392127, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865392193, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_06E5AC6350BD5612.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865392304, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865392405, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8D8F81C2AD007FF4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865392522, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865392622, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_DBE3A542E9C167DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865392716, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865392804, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_97D4D89498459919.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865392906, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865392978, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_A79420259383E98A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865393089, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865393150, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0E99BC6E1964CACE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865393271, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C166689E8CA2A04E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865393357, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865393431, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_32CA5EEFEC612C27.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865393543, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_395D75F92AC91BB0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865393661, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865393750, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_6C262D80C93BD4F0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865393888, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865393996, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751883865394221, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751883865394412, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865394471, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751883865394643, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751883865394697, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751883865394818, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865394941, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865395065, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865395166, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865395248, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_11476E2D9783C34A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865395436, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751883865395587, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751883865395640, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865395693, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865395763, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865395854, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396000, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396100, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396192, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396267, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396345, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396421, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396536, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396646, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396724, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865396877, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865397036, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865397234, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865397330, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865397474, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865397639, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865397728, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865397832, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865397916, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865398023, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865398128, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865398194, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751883865398331, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865398423, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865398519, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865398602, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865398691, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865398763, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865398872, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865399019, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865400062, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865400962, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865401891, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865402910, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865403783, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865404626, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865405468, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865406289, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865407292, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865408276, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865409414, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865410293, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865411264, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865412083, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865413260, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@494a3b4e73a9/Editor/SkinningModule/GenerateGeometryTool.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751883865413057, "dur": 1769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865414827, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865415838, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865416876, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865418029, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865419158, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865420344, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865421199, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865422314, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865423294, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865424343, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865425276, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865426307, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865427328, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865427932, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865429122, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865429534, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865429747, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865430329, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865430624, "dur": 2244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865432869, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865432973, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865433033, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865433307, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865434113, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865434361, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865434415, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865434471, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865435129, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865435300, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865435677, "dur": 1499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865437176, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865437281, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751883865437441, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865438464, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865438560, "dur": 4608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1751883865443235, "dur": 1251, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865558114, "dur": 482, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865444800, "dur": 113813, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1751883865559742, "dur": 3674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865563417, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865563635, "dur": 2115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865565751, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865565848, "dur": 4561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865570409, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865570473, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865572930, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865573092, "dur": 5854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865578948, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865579043, "dur": 5955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865584999, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865585274, "dur": 1675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865586949, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865587101, "dur": 2829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865589930, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865590043, "dur": 2117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865592160, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865592286, "dur": 2792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865595078, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865595163, "dur": 2519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865597683, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865597792, "dur": 5812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751883865603661, "dur": 162712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751883865766422, "dur": 345, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751883865766769, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865338842, "dur": 51077, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865389922, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5CDB3D0CA3CB09B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865390263, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865390340, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_E7D994A1187DB9B1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865390536, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865390778, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_013EC6C429305933.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865390981, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_54E2C669D74DB25A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865391213, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865391349, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_161CFFFA16B8213C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865391509, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865391576, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_061233166775A3D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865391715, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865391793, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_845B1BE9B09512AE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865391899, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865392008, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C30E9AD099929D47.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865392136, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865392214, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A49295611C727860.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865392304, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865392411, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_15FE89EB11B2B491.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865392523, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865392630, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_A3EA01C4850945EE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865392717, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865392820, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_875874FE279274C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865392920, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865392989, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_293131879DA5DD14.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865393091, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865393160, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751883865393549, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_ED2AB5385077AD72.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865393687, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865393776, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_857F5E2A119BA0C9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865393912, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865394007, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865394067, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751883865394243, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865394321, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751883865394555, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751883865394825, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751883865394953, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865395097, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865395231, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865395337, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865395413, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865395527, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751883865395811, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865395958, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865396061, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865396170, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865396249, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865396328, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865396385, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751883865396585, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865396687, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865396830, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865396918, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865397069, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865397196, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865397315, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865397487, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865397596, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865397686, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865397814, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865397882, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865397995, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398088, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398190, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398253, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398334, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398435, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398522, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398615, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398699, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398812, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865398915, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865399042, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865400076, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865400979, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865401944, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865402943, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865403819, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865404638, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865405475, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865406291, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865407281, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865408259, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865409417, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865409496, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865410314, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865411312, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865412149, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865413277, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@a55da47cc43f/Runtime/2D/AssemblyInfo.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751883865413170, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865414944, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865415896, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865416920, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865418078, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865419234, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865420394, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865421265, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865422401, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865423393, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865424405, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865425383, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865426398, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865427494, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865428365, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865429342, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865429511, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865429754, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865430486, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865430925, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865430985, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865431261, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865431396, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865431507, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865431568, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865431630, "dur": 2387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865434018, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865434248, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865435049, "dur": 1481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865436530, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865436715, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865436800, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865436963, "dur": 1000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865437963, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865438073, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865438256, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865438326, "dur": 1863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865440189, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865440329, "dur": 1202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865441532, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865441694, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865441937, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865441996, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865442125, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865442258, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865442356, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865442514, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865442689, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865442754, "dur": 1492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865444246, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865444337, "dur": 1575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865445914, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751883865446417, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865447248, "dur": 112505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865559756, "dur": 2975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865562732, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865562824, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865565459, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865565652, "dur": 3585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865569238, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865569312, "dur": 10084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865579397, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865579557, "dur": 9141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865588699, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865588876, "dur": 1985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865590914, "dur": 6538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865597452, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865597509, "dur": 6094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751883865603654, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751883865603836, "dur": 162993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751883865768724, "dur": 574, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 75155, "tid": 7394, "ts": 1751883865780600, "dur": 3619, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 75155, "tid": 7394, "ts": 1751883865784291, "dur": 1475, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 75155, "tid": 7394, "ts": 1751883865777892, "dur": 8350, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}